import React, { useEffect, use<PERSON><PERSON>back, useContext, useMemo, type JSX } from "react";
import { AdminModelPortfolioResponse, ModelPortfolioStatus } from "@rubiconcarbon/shared-types";
import { Box, Stack, Typography, TextField, IconButton, SxProps } from "@mui/material";
import { isEmpty } from "lodash";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AxiosContext } from "@providers/axios-provider";
import { Maybe, Nullable } from "@rubiconcarbon/frontend-shared";
import integerFormat from "@utils/formatters/integer-format";
import { currencyFormat } from "@rubiconcarbon/frontend-shared";
import { MISSING_DATA } from "@constants/constants";
import Decimal from "decimal.js";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useForm } from "react-hook-form";
import { PriceModel } from "./price-model";
import { NumericFormat } from "react-number-format";
import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import COLORS from "@components/ui/theme/colors";
import { useMediaQuery } from "@mui/material";

import classes from "../../styles/portfolio-sandbox-price.module.scss";

export function convertPriceToNumber(price: string): number {
  return +price.replaceAll(",", "").replaceAll("$", "");
}

const smallScreenStyle: SxProps = {
  width: "100%",
  justifyContent: "flex-start",
  backgroundColor: "white",
  padding: "10px",
};

const baseStyle: SxProps = {
  width: "100%",
  justifyContent: "flex-start",
};

export default function PriceEdit(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  totalQuantity: number;
  onCancelHandler: () => void;
  onSuccessHandler: () => void;
}): JSX.Element {
  const { mockPortfolio, totalQuantity, onCancelHandler, onSuccessHandler } = props;
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { api } = useContext(AxiosContext);
  const isSmallScreen = useMediaQuery("(max-width: 1300px)");
  const PriceResolver = useMemo(() => classValidatorResolver(PriceModel), []);

  const {
    handleSubmit,
    formState: { errors, isDirty },
    control,
    reset,
    watch,
    setValue,
  } = useForm<PriceModel>({
    resolver: PriceResolver,
    mode: "onSubmit",
    defaultValues: new PriceModel(),
  });

  const priceValue = watch("price");
  const priceWithRiskValue = watch("priceWithRiskAdj");

  useEffect(() => {
    if (!isEmpty(priceValue)) {
      setValue("priceWithRiskAdj", "");
    }
  }, [priceValue, setValue]);

  useEffect(() => {
    if (!isEmpty(priceWithRiskValue)) {
      setValue("price", "");
    }
  }, [priceWithRiskValue, setValue]);

  useEffect(() => {
    if (mockPortfolio) {
      reset({
        price: !mockPortfolio?.includeRiskAdjustment
          ? !!mockPortfolio?.priceEstimate && totalQuantity > 0
            ? new Decimal(mockPortfolio?.priceEstimate).dividedBy(totalQuantity).toFixed(2, Decimal.ROUND_HALF_UP)
            : ""
          : "",
        priceWithRiskAdj: mockPortfolio?.includeRiskAdjustment
          ? !!mockPortfolio?.priceEstimate && totalQuantity > 0
            ? new Decimal(mockPortfolio?.priceEstimate).dividedBy(totalQuantity).toFixed(2, Decimal.ROUND_HALF_UP)
            : ""
          : "",
      });
    }
  }, [mockPortfolio, totalQuantity, reset]);

  const totalPrice: Nullable<Decimal> = useMemo(() => {
    const pricePerTonne = !isEmpty(priceValue) ? priceValue : priceWithRiskValue;
    return !!pricePerTonne && totalQuantity > 0
      ? new Decimal(convertPriceToNumber(pricePerTonne) * totalQuantity)
      : null;
  }, [priceValue, priceWithRiskValue, totalQuantity]);

  const onSubmitHandler = useCallback(async () => {
    const pricePerTonne = !isEmpty(priceValue) ? priceValue : priceWithRiskValue;
    const priceEstimate = convertPriceToNumber(pricePerTonne) * totalQuantity;

    const payload = {
      includeRiskAdjustment: !isEmpty(priceWithRiskValue) ? true : false,
      priceEstimate,
      status: ModelPortfolioStatus.QUOTE_READY,
    };

    try {
      await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      enqueueSuccess("Successfully updated portfolio price");
      onSuccessHandler();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to update portfolio price");
    }
  }, [
    priceValue,
    priceWithRiskValue,
    enqueueSuccess,
    enqueueError,
    api,
    onSuccessHandler,
    mockPortfolio?.id,
    totalQuantity,
  ]);

  const cancelHandler = useCallback(() => {
    onCancelHandler();
  }, [onCancelHandler]);

  return (
    <>
      <Maybe condition={!!mockPortfolio}>
        <form id="edit-price-form" onSubmit={handleSubmit(onSubmitHandler)}>
          <Stack
            direction={isSmallScreen ? "column" : "row"}
            gap={isSmallScreen ? 3 : 4}
            sx={isSmallScreen ? smallScreenStyle : baseStyle}
          >
            <Stack direction="row" gap={2}>
              <Typography variant="body2" className={classes.subTitleValueLabel}>
                Quantity:
              </Typography>
              <Typography className={classes.subTitle}>{integerFormat(totalQuantity?.toString())}</Typography>
            </Stack>
            {/* Price */}
            <fieldset style={{ display: "contents" }}>
              <Controller
                control={control}
                name="price"
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <NumericFormat
                    sx={{ marginTop: "-10px" }}
                    size="small"
                    decimalScale={2}
                    allowNegative={false}
                    thousandSeparator
                    prefix="$"
                    label="Price without Risk Adjustment"
                    value={value}
                    customInput={TextField}
                    error={!!errors.price}
                    helperText={errors.price?.message}
                    inputProps={{ maxLength: 10 }}
                    InputProps={{
                      ref,
                      style: { fontSize: 16 },
                    }}
                    {...otherProps}
                  />
                )}
              />
            </fieldset>
            {/* Price With Risk Adj */}
            <fieldset style={{ display: "contents" }}>
              <Controller
                control={control}
                name="priceWithRiskAdj"
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <NumericFormat
                    sx={{ marginTop: "-10px" }}
                    size="small"
                    allowNegative={false}
                    thousandSeparator
                    prefix="$"
                    decimalScale={2}
                    label="Price with Risk Adjustment"
                    value={value}
                    customInput={TextField}
                    error={!!errors.priceWithRiskAdj}
                    helperText={errors.priceWithRiskAdj?.message}
                    inputProps={{ maxLength: 10 }}
                    InputProps={{
                      ref,
                      style: { fontSize: 16, width: "220px" },
                    }}
                    {...otherProps}
                  />
                )}
              />
            </fieldset>
            <Stack direction="row" gap={2} sx={{ minWidth: "150px" }}>
              <Typography variant="body2" className={classes.subTitleValueLabel}>
                Total:
              </Typography>
              <Typography className={classes.subTitle}>
                {totalPrice ? currencyFormat(totalPrice.toString()) : MISSING_DATA}
              </Typography>
            </Stack>

            <Stack direction="row" gap={2} sx={{ marginTop: "-10px" }}>
              <Box>
                <IconButton disabled={!isDirty} type="submit" sx={{ color: "rgba(154, 198, 106, 1)" }} edge="start">
                  <CheckIcon />
                </IconButton>
              </Box>
              <Box>
                <IconButton sx={{ color: COLORS.red }} edge="start" onClick={cancelHandler}>
                  <CancelIcon />
                </IconButton>
              </Box>
            </Stack>
          </Stack>
        </form>
      </Maybe>
    </>
  );
}
