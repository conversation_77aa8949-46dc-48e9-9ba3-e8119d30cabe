import React, { type JSX, use<PERSON><PERSON>back, use<PERSON>ontext, useMemo } from "react";
import { AdminModelPortfolioResponse, ModelPortfolioStatus } from "@rubiconcarbon/shared-types";
import { currencyFormat, Maybe } from "@rubiconcarbon/frontend-shared";
import { Box, Divider, IconButton, Stack, Typography } from "@mui/material";
import integerFormat from "@utils/formatters/integer-format";
import CheckIcon from "@mui/icons-material/Check";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { NOT_APPLICABLE } from "@constants/constants";
import PortfolioSandboxPDF from "../portfolio-sandbox-pdf/portfolio-sandbox-pdf";
import { keyframes, styled } from "@mui/material/styles";
import WatchLaterIcon from "@mui/icons-material/WatchLater";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";

import classes from "../../styles/portfolio-sandbox-price.module.scss";

const approvalColor = "#16A163";

const blink = keyframes`
  0% { border-color: ${approvalColor}; box-shadow: 0 0 20px ${approvalColor}; }
  50% { border-color: transparent; box-shadow: none; }
  100% { border-color: ${approvalColor}; box-shadow: 0 0 20px ${approvalColor}; }
`;

// Define the keyframes for the text's blinking animation.
const textBlink = keyframes`
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
`;

const AnimatedIconButton = styled(IconButton)`
  animation: ${blink} 2.5s linear infinite;
  display: flex;
  align-items: center;
  gap: 6px; /* Adds space between the icon and text */
  border: 1px solid ${approvalColor}; /* Initial border style */
  border-radius: 4px; /* Rounded corners for the border */
`;

const StaticTextBox = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 10px;
  height: 30px;
`;

// Create a styled Typography component to apply the blinking animation to the text.
const AnimatedTypography = styled(Typography)`
  animation: ${textBlink} 2.5s linear infinite;
`;

export default function PortfolioSandboxPrice(props: {
  mockPortfolio: any; //type will be converted to AdminModelPortfolioResponse in the next sprint
  totalQuantity: number;
  isRestricted: boolean;
  isEditInProgress: boolean;
  onSuccessHandler: () => void;
}): JSX.Element {
  const { mockPortfolio, totalQuantity, isRestricted, onSuccessHandler, isEditInProgress } = props;
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const isMissingPrice: boolean = mockPortfolio?.modelPortfolioComponents?.some(
    (element: any) => element.overrideMTM === null && (element?.markupPrice === undefined || element?.markupPrice === null),
  );

  const priceEstimate: number = useMemo(() => {
    if (mockPortfolio?.priceEstimate) {
      return +mockPortfolio?.priceEstimate;
    } else {
      if (isMissingPrice) return 0;

      let amount = 0;
      mockPortfolio?.modelPortfolioComponents?.forEach((c: any) => {
        !!c?.overrideMTM
          ? (amount += c.amountAllocated * +c.overrideMTM)
          : (amount += c.amountAllocated * +c.markupPrice);
      });
      return amount;
    }
  }, [mockPortfolio?.priceEstimate, mockPortfolio?.modelPortfolioComponents, isMissingPrice]);

  const onSubmitHandler = useCallback(async () => {
    const payload = {
      includeRiskAdjustment: false,
      priceEstimate,
      status: ModelPortfolioStatus.QUOTE_READY,
    };

    try {
      await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      enqueueSuccess("Successfully updated portfolio price");
      onSuccessHandler();
    } catch (error: any) {
      if (!!error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to update portfolio price");
    }
  }, [enqueueSuccess, enqueueError, priceEstimate, api, onSuccessHandler, mockPortfolio?.id]);
  return (
    <Maybe condition={!!mockPortfolio}>
      <Stack
        mt={2}
        direction="row"
        gap={5}
        sx={{ backgroundColor: "rgba(238, 238, 238, 1)", padding: 0, width: "100%", borderRadius: 1.5 }}
      >
        <Stack direction="row" gap={2} sx={{ padding: 2.5, paddingTop: 3 }}>
          <Typography variant="body1" className={classes.subTitleValueLabel}>
            Quantity:
          </Typography>
          <Typography variant="body1" className={classes.subTitle}>
            {integerFormat(totalQuantity?.toString())}
          </Typography>
        </Stack>
        <Stack direction="row" gap={2}>
          <Divider orientation="vertical" flexItem sx={{ height: "100%", borderWidth: 1 }} />

          <Stack gap={0} sx={{ padding: 2.5, paddingTop: 2.5 }}>
            <Stack direction="row" gap={1}>
              <Typography variant="body1" className={classes.subTitleValueLabel}>
                Total:
              </Typography>
              <Typography variant="body1" className={classes.subTitleValueLabel}>
                {isMissingPrice ? (
                  NOT_APPLICABLE
                ) : isRestricted && !mockPortfolio?.priceEstimate ? (
                  <span style={{ filter: `blur(3px)` }}>00000</span>
                ) : (
                  currencyFormat(priceEstimate?.toString())
                )}
              </Typography>
            </Stack>

            {!!mockPortfolio?.priceEstimate ? (
              <Stack direction="row" gap={0.2}>
                <CheckCircleIcon sx={{ fontSize: "12px", marginTop: "1px", color: approvalColor }} />
                <Typography variant="body2" className={classes.supportingText} sx={{ color: approvalColor }}>
                  Approved
                </Typography>
              </Stack>
            ) : (
              <Stack direction="row" gap={0.2}>
                <WatchLaterIcon sx={{ fontSize: "12px", marginTop: "1px" }} />
                <Typography variant="body2" className={classes.supportingText}>
                  Pending Approval
                </Typography>
              </Stack>
            )}
          </Stack>

          <Maybe
            condition={
              !isRestricted &&
              !mockPortfolio?.priceEstimate &&
              !isMissingPrice &&
              mockPortfolio?.modelPortfolioComponents?.length > 0
            }
          >
            {!isEditInProgress ? (
              <Box mt={2.5}>
                <AnimatedIconButton
                  disabled={!!mockPortfolio?.priceEstimate}
                  color="primary"
                  onClick={onSubmitHandler}
                  sx={{ height: "35px", color: approvalColor }}
                >
                  <CheckIcon sx={{ fontSize: "22px" }} />
                  <Typography variant="body1" color="inherit">
                    Approve Price
                  </Typography>
                </AnimatedIconButton>
              </Box>
            ) : (
              <Box mt={2.5}>
                <StaticTextBox>
                  <AnimatedTypography variant="body1" color="#3D8DF5">
                    Editing in Progress
                  </AnimatedTypography>
                </StaticTextBox>
              </Box>
            )}
          </Maybe>

          <Maybe condition={!!mockPortfolio?.priceEstimate}>
            <Box mt={1.2} ml={1}>
              <PortfolioSandboxPDF portfolioId={mockPortfolio?.id} portfolioName={mockPortfolio?.name} />
            </Box>
          </Maybe>
        </Stack>
      </Stack>
    </Maybe>
  );
}
