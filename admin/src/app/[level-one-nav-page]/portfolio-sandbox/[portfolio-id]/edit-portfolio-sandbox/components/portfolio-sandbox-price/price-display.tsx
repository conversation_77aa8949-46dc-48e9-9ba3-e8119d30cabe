import React, { useMemo, type JSX } from "react";
import { AdminModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { Stack, Typography } from "@mui/material";
import { Maybe, Nullable } from "@rubiconcarbon/frontend-shared";
import integerFormat from "@utils/formatters/integer-format";
import { currencyFormat } from "@rubiconcarbon/frontend-shared";
import { MISSING_DATA } from "@constants/constants";
import Decimal from "decimal.js";

import classes from "../../styles/portfolio-sandbox-price.module.scss";

export default function PriceDisplay(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  totalQuantity: number;
}): JSX.Element {
  const { mockPortfolio, totalQuantity } = props;

  const price: Nullable<Decimal> = useMemo(
    () =>
      !mockPortfolio?.includeRiskAdjustment
        ? !!mockPortfolio?.priceEstimate && totalQuantity > 0
          ? new Decimal(mockPortfolio?.priceEstimate).dividedBy(totalQuantity)
          : null
        : null,
    [mockPortfolio?.priceEstimate, totalQuantity, mockPortfolio?.includeRiskAdjustment],
  );

  const priceWithRisk: Nullable<Decimal> = useMemo(
    () =>
      mockPortfolio?.includeRiskAdjustment
        ? !!mockPortfolio?.priceEstimate && totalQuantity > 0
          ? new Decimal(mockPortfolio?.priceEstimate).dividedBy(totalQuantity)
          : null
        : null,
    [mockPortfolio?.priceEstimate, totalQuantity, mockPortfolio?.includeRiskAdjustment],
  );

  return (
    <>
      <Maybe condition={!!mockPortfolio}>
        <Stack direction="row" gap={5}>
          <Stack direction="row" gap={2}>
            <Typography variant="body2" className={classes.subTitleValueLabel}>
              Quantity:
            </Typography>
            <Typography className={classes.subTitle}>{integerFormat(totalQuantity?.toString())}</Typography>
          </Stack>
          <Stack direction="row" gap={2}>
            <Typography variant="body2" className={classes.subTitleValueLabel}>
              Price without Risk Adjustment:
            </Typography>
            <Typography className={classes.subTitle}>
              {price ? currencyFormat(price.toFixed(2, Decimal.ROUND_HALF_UP)) : MISSING_DATA}
            </Typography>
          </Stack>
          <Stack direction="row" gap={2}>
            <Typography variant="body2" className={classes.subTitleValueLabel}>
              Price with Risk Adjustment:
            </Typography>
            <Typography className={classes.subTitle}>
              {priceWithRisk ? currencyFormat(priceWithRisk.toFixed(2, Decimal.ROUND_HALF_UP)) : MISSING_DATA}
            </Typography>
          </Stack>
          <Stack direction="row" gap={2}>
            <Typography variant="body2" className={classes.subTitleValueLabel}>
              Total:
            </Typography>
            <Typography className={classes.subTitle}>
              {mockPortfolio?.priceEstimate ? currencyFormat(mockPortfolio?.priceEstimate?.toString()) : MISSING_DATA}
            </Typography>
          </Stack>
        </Stack>
        <Stack
          mt={2}
          direction="row"
          gap={5}
          sx={{ backgroundColor: "rgba(238, 238, 238, 1)", padding: 2, width: "100%" }}
        >
          <Stack direction="row" gap={2}>
            <Typography variant="body2" className={classes.subTitleValueLabel}>
              Quantity:
            </Typography>
            <Typography className={classes.subTitle}>{integerFormat(totalQuantity?.toString())}</Typography>
          </Stack>
        </Stack>
      </Maybe>
    </>
  );
}
