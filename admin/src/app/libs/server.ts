import "server-only";

import { cookies } from "next/headers";
import { getCookie } from "cookies-next/server";
import { GenericRecord, Undefinable } from "@rubiconcarbon/frontend-shared";
import { HttpStatusMapper } from "@constants/http";
import { environment } from "@environment";
import { AUTH_COOKIE_NAME } from "@constants/auth-constants";
import { gunzipSync } from "zlib";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

/**
 * The function retrieves an authentication token from a cookie and returns it as a string or null.
 *
 * @returns The function `getAuthToken` returns a Promise that resolves to a string or null.
 */
export async function getAuthToken(): Promise<string | null> {
  const cookie = await getCookie(AUTH_COOKIE_NAME, { cookies });

  try {
    const decoded = gunzipSync(Uint8Array.from(Buffer.from(cookie ?? "", "base64"))).toString();
    const data: { token?: string } = JSON.parse(decoded);

    return data?.token ?? null;
  } catch {
    return null;
  }
}

/**
 * The function retrieves user permissions from a cookie and returns them as an array of PermissionEnum
 * values or null.
 *
 * @returns The function `getUserPermissions` returns a Promise that resolves to an array of
 * `PermissionEnum` values or null.
 */
export async function getUserPermissions(): Promise<PermissionEnum[] | null> {
  const cookie = await getCookie(AUTH_COOKIE_NAME, { cookies });

  try {
    const decoded = gunzipSync(Uint8Array.from(Buffer.from(cookie ?? "", "base64"))).toString();
    const data: { permissions?: PermissionEnum[] } = JSON.parse(decoded);

    return data?.permissions ?? null;
  } catch {
    return null;
  }
}

type ServerRequestOptions = RequestInit & {
  cache?: RequestCache;
  onResponse?: (response: Response) => Promise<Response> | Response;
};

/**
 * The function checks if a user is logged in by retrieving their authentication token.
 *
 * @returns A Promise that resolves to a boolean value indicating whether the user is logged in or not.
 */
export async function isLoggedIn(): Promise<boolean> {
  const token = await getAuthToken();
  return !!token;
}

/**
 * The function `serverFetch` is an asynchronous function that makes a server-side fetch request with
 * authentication and returns the response data.
 *
 * @param url - The URL of the resource to fetch.
 * @param options - Optional fetch options.
 * @returns A Promise that resolves to the response data.
 */
export async function serverFetch<T>(url: string, options: ServerRequestOptions = {}): Promise<Undefinable<T>> {
  const { cache = "no-cache", onResponse, ...fetchOptions } = options;
  const token = await getAuthToken();

  if (!token) return undefined;

  const res = await fetch(url, {
    ...fetchOptions,
    cache,
    headers: {
      ...fetchOptions.headers,
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!res.ok) {
    throw new Error(HttpStatusMapper[res.status] || `Failed to fetch: ${res.status}`);
  }

  // Apply the onResponse callback to the response if provided
  const processedResponse = onResponse ? await onResponse(res.clone()) : res;

  // Parse the JSON from the (potentially modified) response
  return await processedResponse.json();
}

/**
 * The function `baseApiRequest` is an asynchronous function that makes a server-side fetch request to
 * a base API with optional parameters and returns the response data.
 *
 * @param path - The `path` parameter is a string that represents the path of the API endpoint you want
 * to fetch. It should be a relative path to the base API URL.
 * @param options - Optional fetch options.
 * @returns A Promise that resolves to the response data.
 */
export async function baseApiRequest<T>(path: string, options: ServerRequestOptions = {}): Promise<Undefinable<T>> {
  return serverFetch<T>(`${environment.server.basApi.ssr}/${path}`, options);
}

/**
 * The function `reportingApiRequest` is an asynchronous function that makes a server-side fetch request
 * to a reporting API with optional parameters and returns the response data.
 *
 * @param path - The `path` parameter is a string that represents the path of the API endpoint you want
 * to fetch. It should be a relative path to the base API URL.
 * @param options - Optional fetch options.
 * @returns A Promise that resolves to the response data.
 */
export async function reportingApiRequest<T>(
  path: string,
  options: ServerRequestOptions = {},
): Promise<Undefinable<T>> {
  return serverFetch<T>(`${environment.server.reportingApi.ssr}/${path}`, options);
}

/**
 * The function `generateQueryParams` creates a query string from an object of key-value pairs.
 * @param {GenericRecord} params - The `generateQueryParams` function takes an object `params` as input
 * and generates a query string by encoding the keys and values of the object. Each key-value pair is
 * encoded using `encodeURIComponent` and then joined together with "&" as the separator.
 */
export const generateQueryParams = (params: GenericRecord = {}): string =>
  Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key]?.toString())}`)
    .join("&");

/**
 * The function replaces path parameters in a URL string with their corresponding values from an
 * object.
 * @param {string} url - A string representing a URL with path parameters.
 * @param {Params} params - The `params` parameter is an object that contains key-value pairs
 * representing query parameters.
 */
export const urlWithPathParams = (url: string, params: GenericRecord = {}): string =>
  Object.keys(params).reduce((prev, curr) => {
    return prev.replace(`{${curr}}`, String(params[curr]));
  }, url);

/**
 * The function generates a path by appending query parameters to a given URL.
 * @param {string} url - The `url` parameter is a string representing the base URL that you want to
 * append query parameters to in order to generate a complete URL.
 * @param {GenericRecord} pathParams - The `pathParams` parameter is an object that contains key-value pairs representing the path parameters to be appended to the URL. These path parameters are used to specify specific resources or endpoints in a RESTful API.
 * @param {GenericRecord} queryParams - The `queryParams` parameter is an object that contains key-value pairs representing the query parameters to be appended to the URL. These query parameters are used to provide additional information to the server when making a request.
 */
export const generatePath = (url: string, pathParams: GenericRecord, queryParams: GenericRecord): string =>
  url ? `${urlWithPathParams(url, pathParams)}${queryParams ? `?${generateQueryParams(queryParams)}` : ""}` : "";
